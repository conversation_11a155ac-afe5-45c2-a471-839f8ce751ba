name: Coverage

env:
  NODE_OPTIONS: --max-old-space-size=6144

on:
  push:
    branches:
      - master
    paths-ignore:
      - 'examples/**'
      - 'website/**'
      - '**/*.md'
  pull_request:
    types:
      - 'opened'
      - 'synchronize'
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  coverage:
    permissions:
      checks: write
      pull-requests: write
      contents: read
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [16.x]

    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: https://registry.npmjs.org/
      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          run_install: false
      - name: Get pnpm store directory
        id: pnpm-cache
        run: |
          echo "pnpm_cache_dir=$(pnpm store path)" >> "$GITHUB_OUTPUT"
      - uses: actions/cache@v3
        name: Setup pnpm cache
        with:
          path: |
            ${{ steps.pnpm-cache.outputs.pnpm_cache_dir }}
            .cache
          key: ${{ runner.os }}-pnpm-store-node-${{ matrix.node-version }}-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-node-${{ matrix.node-version }}
      - run: npm run setup
      - run: npm run cov
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        env:
          CI: true
