name: Version

on:
  push:
    branches:
      - release/next

jobs:
  version:
    name: Version
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [16]

    steps:
      - name: Checkout Branch
        uses: actions/checkout@v3

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'pnpm'

      - name: Install Dependencies
        run: pnpm install

      - name: Setup git user
        run: pnpm run setup-git

      - name: Create Release Pull Request
        uses: changesets/action@v1
        with:
          version: pnpm run version
          commit: 'chore: update versions'
          title: 'chore: update versions'
          setupGitUser: false
        env:
          GITHUB_TOKEN: ${{ secrets.PERSONAL_GITHUB_TOKEN }}
