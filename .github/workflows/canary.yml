name: Publish canary

on:
  push:
    branches:
      - release/**

jobs:
  check_changeset:
    name: Check Changeset exists
    outputs:
      status: ${{ steps.check.outcome }}
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Branch
        uses: actions/checkout@v4

      - name: Check
        id: check
        continue-on-error: true
        run: test "$(ls -1 .changeset | wc -l)" -gt "2"

  canary:
    name: Publish Canary
    runs-on: ubuntu-latest
    needs: check_changeset
    if: needs.check_changeset.outputs.status == 'success'

    strategy:
      matrix:
        node-version: [18]

    steps:
      - name: Checkout Branch
        uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'pnpm'

      - name: Setup
        run: pnpm run setup

      - name: Config npm
        run: echo "//registry.npmjs.org/:_authToken=${NPM_TOKEN}" > .npmrc
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

      - run: pnpm run release:snapshot
