name: Release

on:
  push:
    branches:
      - master

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [16]

    steps:
      - name: Checkout Branch
        uses: actions/checkout@v3

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'pnpm'

      - name: Setup
        run: pnpm run setup

      - name: Setup git user
        run: pnpm run setup-git

      - name: Publish to npm
        id: changesets
        uses: changesets/action@v1
        with:
          version: pnpm run version
          commit: 'chore: update versions'
          title: 'chore: update versions'
          publish: pnpm release
          createGithubReleases: false
          setupGitUser: false
        env:
          GITHUB_TOKEN: ${{ secrets.PERSONAL_GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
