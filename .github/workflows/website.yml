name: Build Website
on:
  push:
    paths:
      - 'website/**'
    branches:
      - master
  workflow_dispatch:

# 任务
jobs:
  build-and-deploy:
    # 服务器环境：最新版 Ubuntu
    runs-on: ubuntu-latest
    steps:
      # 拉取代码
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - name: Use Node.js 16.x
        uses: actions/setup-node@v3
        with:
          node-version: 16.x
          cache: 'pnpm'

      - run: pnpm install --filter=./website

      - run: cd website && pnpm build

      # 部署到 GitHub Pages
      - name: Deploy
        uses: JamesIves/github-pages-deploy-action@4.1.0
        if: github.ref == 'refs/heads/master'
        with:
          BRANCH: gh-pages
          FOLDER: website/build
          clean-exclude: ice.png
