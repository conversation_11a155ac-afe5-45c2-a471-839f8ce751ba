{"name": "@examples/canvas-project", "version": "1.0.0", "private": true, "scripts": {"start": "ice start", "build": "ice build"}, "description": "", "author": "", "license": "MIT", "dependencies": {"@ice/app": "workspace:*", "@ice/plugin-canvas": "workspace:*", "@ice/runtime": "workspace:*", "react": "^18.0.0", "react-dom": "^18.0.0", "@ice/cache-canvas": "workspace:*"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "webpack": "^5.88.0"}}