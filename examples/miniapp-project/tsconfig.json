{"compileOnSave": false, "buildOnSave": false, "compilerOptions": {"baseUrl": ".", "outDir": "build", "module": "esnext", "target": "es6", "jsx": "react-jsx", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "lib": ["es6", "dom"], "sourceMap": true, "allowJs": true, "rootDir": "./", "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": false, "importHelpers": true, "strictNullChecks": true, "suppressImplicitAnyIndexErrors": true, "noUnusedLocals": true, "skipLibCheck": true, "paths": {"@/*": ["./src/*"], "ice": [".ice"]}}, "include": ["src", ".ice", "ice.config.*"], "exclude": ["node_modules", "build", "public"]}