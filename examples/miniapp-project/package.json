{"name": "@examples/miniapp-project", "private": true, "version": "1.0.0", "scripts": {"start": "ice start", "start:wechat": "ice start --target wechat-miniprogram", "start:ali": "ice start --target ali-miniapp", "build": "ice build", "build:wechat": "ice build --target wechat-miniprogram", "build:ali": "ice build --target ali-miniapp"}, "description": "", "author": "", "license": "MIT", "dependencies": {"@ice/app": "workspace:*", "@ice/runtime": "workspace:*", "@ice/miniapp-html-styles": "workspace:*", "@ice/plugin-miniapp": "workspace:*", "@ice/miniapp-runtime": "workspace:*", "ahooks": "^3.3.8", "react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.2", "browserslist": "^4.19.3", "speed-measure-webpack-plugin": "^1.5.0", "webpack": "^5.88.0"}}