import{d as f,r as h,o as a,c,a as i,w as m,b as e,p as v,e as g,_ as b,f as p,g as H,t as k,h as C,i as d,j as F,k as V,l as I,m as x}from"./vendor.js";var _=(t,o)=>{const u=t.__vccOpts||t;for(const[s,r]of o)u[s]=r;return u};const l=t=>(v("data-v-123114aa"),t=t(),g(),t),N={class:"wrapper"},D=l(()=>e("img",{alt:"Vue logo",src:"https://gw.alicdn.com/imgextra/i2/O1CN01y9FKOg1f0OnH6Hew8_!!6000000003944-2-tps-200-200.png"},null,-1)),L=l(()=>e("br",null,null,-1)),P=p(" Home "),S=l(()=>e("br",null,null,-1)),j=p(" List "),A=l(()=>e("br",null,null,-1)),O=f({setup(t){const o=()=>{b.push("/")};return(u,s)=>{const r=h("router-link"),w=h("router-view");return a(),c("div",N,[D,L,i(r,{to:"/"},{default:m(()=>[P]),_:1}),S,i(r,{to:"/list"},{default:m(()=>[j]),_:1}),A,e("button",{onClick:o},"\u5FAE\u5E94\u7528\u95F4\u8DF3\u8F6C"),i(w)])}}});var y=_(O,[["__scopeId","data-v-123114aa"]]);const B=t=>(v("data-v-3cf51fd6"),t=t(),g(),t),E=B(()=>e("p",null,[e("a",{href:"https://v3.vuejs.org/",target:"_blank"}," Learn Vue 3 "),p(" | "),e("a",{href:"https://micro-frontends.ice.work/",target:"_blank"},"icestark Docs")],-1)),W=f({props:{msg:null},setup(t){const o=H(0);return(u,s)=>(a(),c("div",null,[e("h1",null,k(t.msg||"Hello Vite + icestark + Vue3!"),1),E,e("button",{type:"button",onClick:s[0]||(s[0]=r=>o.value++)},"count is: "+k(o.value),1)]))}});var R=_(W,[["__scopeId","data-v-3cf51fd6"]]);const T={};function K(t,o){return a(),c("h1",null,"List Page")}var M=_(T,[["render",K]]);const q={};function z(t,o){return a(),c("h1",null,"Detail Page")}var G=_(q,[["render",z]]);const J={};function Q(t,o){return a(),c("h1",null,"404 Page")}var U=_(J,[["render",Q]]);const X=()=>new Promise(t=>{F(),t(!0)}),$=()=>{const t=V(d()?I():"/");return C({history:t,routes:[{path:"/",component:R},{path:"/list",component:M},{path:"/detail",component:G},{path:"/:pathMatch(.*)",component:d()?()=>X():U}]})};let n=null;d()||(n=x(y),n.use($()),n.mount("#app"));function Z({container:t}){n=x(y),n.use($()),n.mount(t)}function tt(){n&&n.unmount()}export{Z as mount,tt as unmount};
