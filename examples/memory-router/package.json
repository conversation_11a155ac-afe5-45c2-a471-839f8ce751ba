{"name": "@examples/memory-router", "private": true, "version": "1.0.0", "scripts": {"start": "ice start", "build": "ice build", "build:optimization": "ice build --config optimization.config.mts"}, "description": "", "author": "", "license": "MIT", "dependencies": {"@ice/app": "workspace:*", "@ice/runtime": "workspace:*", "react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0"}}