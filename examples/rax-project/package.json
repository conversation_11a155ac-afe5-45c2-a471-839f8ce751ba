{"name": "@examples/rax-project", "private": true, "version": "1.0.0", "scripts": {"start": "ice start", "build": "ice build"}, "description": "", "author": "", "license": "MIT", "dependencies": {"@ice/app": "workspace:*", "@ice/plugin-rax-compat": "workspace:*", "@ice/plugin-jsx-plus": "workspace:*", "@ice/appear": "workspace:*", "@ice/runtime": "workspace:*", "rax": "^1.2.2", "rax-image": "^2.4.1", "rax-is-valid-element": "^1.0.0", "rax-text": "^2.2.0", "rax-view": "^2.3.0", "react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.2", "webpack": "^5.88.0"}}