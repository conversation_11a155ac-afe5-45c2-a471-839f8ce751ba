{"name": "@examples/with-antd", "private": true, "version": "1.0.0", "scripts": {"start": "ice start", "build": "ice build"}, "description": "", "author": "", "license": "MIT", "dependencies": {"@ice/app": "workspace:*", "@ice/plugin-antd": "workspace:*", "@ice/plugin-moment-locales": "workspace:*", "@ice/runtime": "workspace:*", "antd": "^4.0.0", "moment": "^2.29.4", "react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.2"}}