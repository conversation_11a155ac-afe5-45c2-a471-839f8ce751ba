{"name": "@examples/with-jest", "private": true, "version": "1.0.0", "scripts": {"start": "ice start", "build": "ice build", "test": "jest"}, "dependencies": {"@ice/runtime": "workspace:*", "react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@ice/app": "workspace:*", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.3.0", "@types/jest": "^29.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.2", "@types/testing-library__jest-dom": "^5.14.5", "jest": "^28.1.3", "jest-environment-jsdom": "^29.0.2", "ts-jest": "^28.0.8", "typescript": "^4.8.2"}}