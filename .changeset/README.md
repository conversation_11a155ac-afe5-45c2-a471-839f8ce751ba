# Changesets

Hello and welcome! This folder has been automatically generated by `@changesets/cli`, a build tool that works
with multi-package repos, or single-package repos to help you version and publish your code. You can
find the full documentation for it [in our repository](https://github.com/changesets/changesets)

We have a quick list of common questions to get you started engaging with this project in
[our documentation](https://github.com/changesets/changesets/blob/main/docs/common-questions.md)


## Add A Changeset

Read the [documentation](https://github.com/changesets/changesets/blob/main/docs/adding-a-changeset.md) for more detail.

## Publish Snapshot Version

> NOTE: It is useful for you to release a version to test your package on your current branch.

Run the following command to publish the snapshot version: 

```bash
$ npm run release:snapshot
```

After that, you don't need to commit the change to the branch.

## Publish Beta Version

> NOTE: Recommend you to publish a beta version on a new branch(such as `release-beta`) which is checked out from the `release*` branch.

Run the following command to publish the beta version: 

```bash
$ npm run release:beta
```

Then, we need to commit the changes to the branch. 

For more detail, please see this [documentation](https://github.com/changesets/changesets/blob/main/docs/prereleases.md).

## Publish Latest Version

GitHub bot will automatically create a PR to update the latest versions for the released package.

<img width="701" alt="image" src="https://user-images.githubusercontent.com/44047106/215980879-965da73d-317e-4576-81ee-118e11bcc2d4.png">

What we need to do is to merge the PR to the `release*` branch. After the `release*` branch was merged to the master branch, the versions will be released automatically.
